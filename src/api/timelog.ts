import useSWR from 'swr';
import { useMemo } from 'react';

import { fDate } from 'src/utils/format-time';
import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';

import { ITimelog, IStaffHours, ITimelogCreate } from 'src/types/timelog';

import { PaginationProps } from './paginate';

export enum TimelogAction {
  START = 'start',
  PAUSE = 'pause',
  RESUME = 'resume',
  STOP = 'stop',
}

export function useGetTimelogs() {
  const URL = endpoints.timelog.list;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      timelogs: (data as ITimelog[]) || [],
      timelogsLoading: isLoading,
      timelogsError: error,
      timelogsValidating: isValidating,
      timelogsEmpty: !isLoading && !data?.length,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function useGetStaffHours({
  limit = 10,
  page = 1,
  sortBy = '',
  search = '',
  filter = {},
}: PaginationProps) {
  // Create URLSearchParams instance
  const params = new URLSearchParams();

  // Add pagination parameters
  params.set('page', String(page));
  params.set('limit', String(limit));

  // Add sort parameter if provided
  if (sortBy) {
    params.set('sortBy', sortBy);
  }

  // Add search parameter if provided
  if (search) {
    params.set('search', search);
  }

  // Format date objects to ISO strings if they exist
  const formattedFilter = { ...filter };
  if (formattedFilter.startDate instanceof Date) {
    formattedFilter.startDate = fDate(formattedFilter.startDate, 'yyyy-MM-dd');
  }
  if (formattedFilter.endDate instanceof Date) {
    formattedFilter.endDate = fDate(formattedFilter.endDate, 'yyyy-MM-dd');
  }

  // Add filter parameters directly
  Object.entries(formattedFilter)
    .filter(([_, value]) => value !== undefined && value !== null && value !== '')
    .forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        // For arrays, set as a comma-separated string
        params.set(key, value.join(','));
      } else {
        // For single values
        params.set(key, String(value));
      }
    });

  const URL = `${endpoints.timelog.listStaffHours}?${params.toString()}`;

  const { data, isLoading, error, isValidating, mutate } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(
    () => ({
      staffHours: (data?.data as IStaffHours[]) || [],
      staffHoursLoading: isLoading,
      staffHoursError: error,
      staffHoursValidating: isValidating,
      staffHoursEmpty: !isLoading && !data?.data?.length,
      staffHoursCurrentPage: data?.meta.currentPage || 0,
      staffHoursItemsPerPage: data?.meta.itemsPerPage || 10,
      staffHoursTotalItems: data?.meta.totalItems || 0,
      staffHoursTotalPages: data?.meta.totalPages || 0,
      staffHoursMutate: mutate,
    }),
    [data, error, isLoading, isValidating, mutate]
  );
  return memoizedValue;
}

export function useGetTimelog(timelogId: string) {
  const URL = timelogId ? [endpoints.timelog.details.replace(':id', timelogId)] : '';
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      timelog: data as ITimelog,
      timelogLoading: isLoading,
      timelogError: error,
      timelogValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export async function startTimelog(timelogData: ITimelogCreate) {
  const URL = endpoints.timelog.start;
  if (!timelogData.action) {
    timelogData.action = TimelogAction.START;
  }

  await axiosInstance.post(URL, timelogData);
}

export async function stopTimelog(timelogData: ITimelogCreate) {
  const URL = endpoints.timelog.stop;
  if (!timelogData.action) {
    timelogData.action = TimelogAction.STOP;
  }

  await axiosInstance.patch(URL, timelogData);
}
