import useSWR from 'swr';
import { useMemo } from 'react';

import { fDate } from 'src/utils/format-time';
import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';

import {
  IUser,
  IStaff,
  IOwner,
  IVacation,
  IUserCreate,
  IUserUpdate,
  IUserValidate,
  IVacationCreate,
  IVacationUpdate,
  IUserCalendarEvent,
} from 'src/types/user';

// const options = {
//   revalidateIfStale: true,
//   revalidateOnFocus: true,
//   revalidateOnReconnect: true,
// };

export const USER_ROLE = {
  admin: 'admin',
  manager: 'manager',
  driver: 'driver',
  user: 'user',
  owner: 'owner',
};

export const USER_PERMISSIONS = {
  isDriver: 'isDriver',
  isVerifier: 'isVerifier',
};

export const USER_STATUS_OPTIONS = [
  { value: 'active', label: 'Activo', plural: 'Activos' },
  { value: 'inactive', label: 'Inactivo', plural: 'Inactivos' },
];

export const USER_ROLE_OPTIONS = [
  { label: 'Administrador', value: USER_ROLE.admin },
  { label: 'Gestor', value: USER_ROLE.manager },
  { label: 'Condutor', value: USER_ROLE.driver },
  { label: 'Maid', value: USER_ROLE.user },
  { label: 'Proprietário', value: USER_ROLE.owner },
];

export function useGetUsers() {
  const URL = endpoints.user.list;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      users: (data as IUser[]) || [],
      usersLoading: isLoading,
      usersError: error,
      usersValidating: isValidating,
      usersEmpty: !isLoading && !data?.length,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function useGetUser(userId: string) {
  const URL = userId ? [endpoints.user.details.replace(':id', userId)] : '';
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      user: data as IUser,
      userLoading: isLoading,
      userError: error,
      userValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function useGetUserVacations(userId: string) {
  const URL = endpoints.user.vacations.replace(':id', userId);
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      vacations: (data as IVacation[]) || [],
      vacationsLoading: isLoading,
      vacationsError: error,
      vacationsValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function getUserVacations(userId: string) {
  const URL = endpoints.user.vacations.replace(':id', userId);
  return axiosInstance.get(URL);
}

export function getUserCalendar(userId: string, month: Date | null) {
  const params = new URLSearchParams();
  if (month) {
    params.set('month', fDate(month || new Date(), 'yyyy-MM'));
  }

  const URL = `${endpoints.user.calendar.replace(':id', userId)}${params ? `?${params.toString()}` : ''}`;
  return axiosInstance.get(URL);
}

export function getUserHoursCalendar(userId: string, month: Date | null) {
  const params = new URLSearchParams();
  if (month) {
    params.set('month', fDate(month || new Date(), 'yyyy-MM'));
  }

  const URL = `${endpoints.user.hoursCalendar.replace(':id', userId)}${params ? `?${params.toString()}` : ''}`;
  return axiosInstance.get(URL);
}

export function useGetStaff(date?: Date) {
  const today = fDate(date || new Date(), 'yyyy-MM-dd');
  const params = new URLSearchParams(`today=${today}&onlyAvailable=true`);

  const URL = `${endpoints.user.staff}?${params.toString()}`;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher, {
    revalidateOnFocus: false, // Prevent revalidation on window focus
    dedupingInterval: 5000,   // Deduplicate requests within 5 seconds
    keepPreviousData: true,   // Keep showing previous data while loading new data
  });

  const memoizedValue = useMemo(() => {
    const users = data?.map((user: IStaff) => ({
      ...user,
      title: `${user.firstName} ${user.lastName}`,
      // textColor: user.color,
    }));

    return {
      users: (users as IStaff[]) || [],
      usersLoading: isLoading,
      usersError: error,
      usersValidating: isValidating,
      usersEmpty: !isLoading && !data?.length,
    };
  }, [data, error, isLoading, isValidating]);

  return memoizedValue;
}

export function getStaffAvailable(date?: Date) {
  const today = fDate(date || new Date(), 'yyyy-MM-dd');
  const params = new URLSearchParams(`today=${today}&onlyAvailable=true`);

  const URL = `${endpoints.user.staff}?${params.toString()}`;
  return fetcher(URL);
}

export function useGetOwners() {
  const URL = endpoints.user.owners;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(() => {
    const owners = data?.map((user: IOwner) => ({
      ...user,
      title: `${user.firstName} ${user.lastName}`,
      // textColor: user.color,
    }));

    return {
      owners: (owners as IOwner[]) || [],
      ownersLoading: isLoading,
      ownersError: error,
      ownersValidating: isValidating,
      ownersEmpty: !isLoading && !data?.length,
    };
  }, [data, error, isLoading, isValidating]);

  return memoizedValue;
}

export function useGetCalendar(userId: string, month: Date | null) {
  const params = new URLSearchParams();
  if (month) {
    params.set('month', fDate(month || new Date(), 'yyyy-MM'));
  }

  const URL = `${endpoints.user.calendar.replace(':id', userId)}${params ? `?${params.toString()}` : ''}`;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      events: (data as IUserCalendarEvent[]) || [],
      eventsLoading: isLoading,
      eventsError: error,
      eventsValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function useGetMyCalendar(month: Date | null) {
  const params = new URLSearchParams();
  if (month) {
    params.append('month', fDate(month || new Date(), 'yyyy-MM'));
  }
  const URL = `${endpoints.user.myCalendar}${params ? `?${params.toString()}` : ''}`;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      events: (data as IUserCalendarEvent[]) || [],
      eventsLoading: isLoading,
      eventsError: error,
      eventsValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function validateUser(userData: IUserValidate) {
  const URL = endpoints.user.validate;
  return axiosInstance.post(URL, userData);
}

export function createUser(userData: IUserCreate) {
  const URL = endpoints.user.create;
  return axiosInstance.post(URL, userData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export function updateUser(userData: IUserUpdate) {
  const URL = endpoints.user.update.replace(':id', userData.id);
  return axiosInstance.patch(URL, userData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export function updateUserAccount(userData: IUserUpdate) {
  const URL = endpoints.auth.me;
  return axiosInstance.patch(URL, userData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export function deleteUser(userId: string) {
  const URL = endpoints.user.delete.replace(':id', userId);
  return axiosInstance.delete(URL);
}

export function deleteUsers(userIds: Array<string>) {
  const URL = endpoints.user.batchDelete;
  return axiosInstance.delete(URL, { data: { ids: userIds } });
}

export function updatePassword(oldPassword: string, newPassword: string) {
  const URL = endpoints.auth.password;
  return axiosInstance.patch(URL, { old: oldPassword, new: newPassword });
}

export function createVacation(vacationData: IVacationCreate) {
  const URL = endpoints.user.vacations.replace(':id', vacationData.user);
  return axiosInstance.post(URL, vacationData);
}

export function updateVacation(vacationData: IVacationUpdate) {
  const URL = endpoints.user.vacation.replace(':id', vacationData.id);
  return axiosInstance.patch(URL, vacationData);
}

export function deleteVacation(vacationId: string) {
  const URL = endpoints.user.vacation.replace(':id', vacationId);
  return axiosInstance.delete(URL);
}
