import { useMemo } from 'react';
import useSWR, { mutate } from 'swr';

import axiosInstance, { fetcher, endpoints } from 'src/utils/axios';

export function useGetOctoratePlaces() {
  const URL = endpoints.octorate.places;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);

  const memoizedValue = useMemo(() => {
    if (!data) {
      return {
        places: [],
        placesLoading: isLoading,
        placesError: error,
        placesValidating: isValidating,
      }
    }

    return {
      places: data.data,
      placesLoading: isLoading,
      placesError: error,
      placesValidating: isValidating,
    };
  }, [data, error, isLoading, isValidating]);

  return memoizedValue;
}

export function useGetOctoratePlace(id: string) {
  const URL = endpoints.octorate.place.replace(':id', id);
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      place: data,
      placeLoading: isLoading,
      placeError: error,
      placeValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export function useGetOctoratePlaceCalendar(id: string, month: string | null) {
  const params = month ? `?month=${month}` : '';
  const URL = `${endpoints.octorate.calendar.replace(':id', id)}${params}`;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      calendar: data,
      calendarLoading: isLoading,
      calendarError: error,
      calendarValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}

export async function markDateAsUnavailable(placeId: string, roomId: string, date: string | null) {
  const URL = endpoints.octorate.calendar.replace(':id', roomId);
  const response = await axiosInstance.post(URL, { date, availability: 0 });

  mutate(endpoints.octorate.calendar.replace(':id', placeId));

  return response;
}

export async function markDateAsAvailable(placeId: string, roomId: string, date: string | null) {
  const URL = endpoints.octorate.calendar.replace(':id', roomId);
  const response = await axiosInstance.post(URL, { date, availability: 1 });

  mutate(endpoints.octorate.calendar.replace(':id', placeId));

  return response;
}

export function useGetOctoratePlaceStatement(placeId: string, startDate: string | null, endDate: string | null) {
  const params = new URLSearchParams();

  if (startDate) {
    params.append('startDate', startDate);
  }

  if (endDate) {
    params.append('endDate', endDate);
  }

  const URL = `${endpoints.octorate.statement.replace(':id', placeId)}${params ? `?${params.toString()}` : ''}`;
  const { data, isLoading, error, isValidating } = useSWR(URL, fetcher);
  const memoizedValue = useMemo(
    () => ({
      statement: data,
      statementLoading: isLoading,
      statementError: error,
      statementValidating: isValidating,
    }),
    [data, error, isLoading, isValidating]
  );
  return memoizedValue;
}
