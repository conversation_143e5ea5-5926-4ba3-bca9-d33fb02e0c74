import axios, { AxiosRequestConfig } from 'axios';

import { HOST_API } from 'src/config-global';

// ----------------------------------------------------------------------

const axiosInstance = axios.create({ baseURL: HOST_API });

axiosInstance.interceptors.response.use(
  (res) => res,
  (error) => Promise.reject((error.response && error.response.data) || 'Something went wrong')
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosInstance.get(url, { ...config });

  return res.data;
};

// ----------------------------------------------------------------------

export const endpoints = {
  // chat: '/api/chat',
  // kanban: '/api/kanban',
  dashboard: {
    overview: '/dashboards/overview',
  },
  // calendar: '/api/calendar',
  auth: {
    me: '/auth/me',
    login: '/auth/login',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    register: '/auth/register',
    password: '/auth/password',
  },
  // mail: {
  //   list: '/api/mail/list',
  //   details: '/api/mail/details',
  //   labels: '/api/mail/labels',
  // },
  // post: {
  //   list: '/api/post/list',
  //   details: '/api/post/details',
  //   latest: '/api/post/latest',
  //   search: '/api/post/search',
  // },
  // product: {
  //   list: '/api/product/list',
  //   details: '/api/product/details',
  //   search: '/api/product/search',
  // },
  job: {
    list: '/jobs',
    listPaginated: '/jobs/all',
    listStatusCounts: '/jobs/status-counts',
    listByPlace: '/jobs/place/:placeId',
    listByUser: '/jobs/user/:userId',
    mine: '/jobs/mine',
    mineActive: '/jobs/mine/active',
    mineAtPlace: '/jobs/mine?place=:placeId',
    details: '/jobs/:id',
    timelogs: '/jobs/:id/timelogs',
    search: '/jobs/search',
    create: '/jobs',
    update: '/jobs/:id',
    delete: '/jobs/:id',
    batchDelete: '/jobs/batch',
    import: '/jobs/import',
    deleteImported: '/jobs/import',
    deltas: '/jobs/deltas',
    verifiable: '/jobs/verifiable',
    verify: '/jobs/verify',
  },
  timelog: {
    list: '/timelogs',
    listStaffHours: '/timelogs/staff-hours',
    details: '/timelogs/:id',
    search: '/timelogs/search',
    start: '/timelogs',
    stop: '/timelogs',
    update: '/timelogs/:id',
    delete: '/timelogs/:id',
    batchDelete: '/timelogs/batch',
  },
  tag: {
    list: '/tags',
    details: '/tags/:id',
    scan: '/tags/scan',
    create: '/tags',
    update: '/tags/:id',
    delete: '/tags/:id',
    batchDelete: '/tags/batch',
  },
  vehicle: {
    list: '/vehicles',
    details: '/vehicles/:id',
    create: '/vehicles',
    update: '/vehicles/:id',
    delete: '/vehicles/:id',
    batchDelete: '/vehicles/batch',
    booking: {
      list: '/vehicles/bookings',
      details: '/vehicles/bookings/:id',
      create: '/vehicles/bookings',
      update: '/vehicles/bookings/:id',
      delete: '/vehicles/bookings/:id',
    },
  },
  user: {
    list: '/users',
    staff: '/users/staff',
    owners: '/users/owners',
    details: '/users/:id',
    vacation: '/users/vacations/:id',
    vacations: '/users/:id/vacations',
    calendar: '/users/:id/calendar',
    hoursCalendar: '/users/:id/hours-calendar',
    myCalendar: '/users/my-calendar',
    search: '/users/search',
    validate: '/users/validate',
    create: '/users',
    update: '/users/:id',
    delete: '/users/:id',
    batchDelete: '/users/batch',
  },
  place: {
    list: '/places',
    details: '/places/:id',
    search: '/places/search',
    create: '/places',
    update: '/places/:id',
    delete: '/places/:id',
    batchUpdate: '/places/batch',
    batchDelete: '/places/batch',
    typology: {
      list: '/places/typologies',
    },
  },
  area: {
    list: '/areas',
    details: '/areas/:id',
    search: '/areas/search',
    create: '/areas',
    update: '/areas/:id',
    delete: '/areas/:id',
    batchDelete: '/areas/batch',
  },
  typology: {
    list: '/places/typologies',
    details: '/places/typologies/:id',
    search: '/places/typologies/search',
    create: '/places/typologies',
    update: '/places/typologies/:id',
    delete: '/places/typologies/:id',
    batchDelete: '/places/batch',
  },
  bag: {
    list: '/bags',
    details: '/bags/:id',
    search: '/bags/search',
    create: '/bags',
    update: '/bags/:id',
    delete: '/bags/:id',
    batchDelete: '/bags/batch',
  },
  asset: {
    list: '/assets',
    collectable: '/assets/collectable',
    details: '/assets/:id',
    search: '/assets/search',
    create: '/assets',
    requests: '/assets/requests',
    request: '/assets/requests/:id',
    update: '/assets/:id',
    delete: '/assets/:id',
    batchDelete: '/assets/batch',
  },
  incident: {
    list: '/incidents',
    details: '/incidents/:id',
    search: '/incidents/search',
    create: '/incidents',
    update: '/incidents/:id',
    delete: '/incidents/:id',
    batchDelete: '/incidents/batch',
  },
  auditlog: {
    list: '/auditlogs',
    listForEntity: '/auditlogs/:entityType/:entityId',
    details: '/auditlogs/:id',
  },
  octorate: {
    places: '/octorate/places',
    place: '/octorate/place/:id',
    calendar: '/octorate/place/:id/calendar',
    statement: '/octorate/place/:id/statement',
  }
};
