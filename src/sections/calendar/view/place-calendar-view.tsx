'use client';

import { useSnackbar } from 'notistack';
import Calendar from '@fullcalendar/react';
import { useState, useEffect } from 'react';
import dayGridPlugin from '@fullcalendar/daygrid';
import ptLocale from '@fullcalendar/core/locales/pt'
import enLocale from '@fullcalendar/core/locales/en-gb'
import { EventResizeDoneArg } from '@fullcalendar/interaction';
import { DateSelectArg, EventClickArg } from '@fullcalendar/core';

import { Box, Stack, Container } from '@mui/system';
import { Card, Button, Divider, Tooltip } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { fCurrency } from 'src/utils/format-number';
import { fDate, paddedMonth } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { useAuthContext } from 'src/auth/hooks';
import { markDateAsAvailable, markDateAsUnavailable, useGetOctoratePlaceCalendar } from 'src/api/octorate';

import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import { OwnerPlaceSelect } from 'src/sections/owner/owner-place-select';

import { ICalendarDate } from 'src/types/calendar';

import { useCalendar } from '../hooks';
import { StyledCalendar } from '../styles';
import CalendarToolbar from '../calendar-toolbar';

type BookingEvent = {
  id: string;
  title: string;
  start: ICalendarDate;
  end: ICalendarDate;
  display: string;
  allDay: boolean;
  editable: boolean;
  overlap: boolean;
  extendedProps: {
    place: string;
    availability: number;
    price: number;
  };
  classNames: string;
};

export default function PlaceCalendarView() {
  const { t, i18n } = useTranslate();
  const { user: authUser } = useAuthContext();
  const confirmAsAvailable = useBoolean();
  const confirmAsUnavailable = useBoolean();
  const [selectedRoom, setSelectedRoom] = useState<string>();
  const [selectedDate, setSelectedDate] = useState<string>();

  const [activePlace, setActivePlace] = useState<string>(authUser?.places[0]?.octorateId || '');

  const {
    date,
    view,
    onDateNext,
    onDatePrev,
    calendarRef,
    onInitialView
  } = useCalendar('dayGridMonth');

  const [month, setMonth] = useState(`${paddedMonth(date)}-${date.getFullYear()}`);
  const { calendar, calendarLoading, calendarError } = useGetOctoratePlaceCalendar(activePlace, month);
  const { enqueueSnackbar } = useSnackbar();

  const renderEventContent = (eventInfo: { event: BookingEvent, timeText: string }) => {
    const { event } = eventInfo;

    return (
      <Box className="fc-event-main-frame">
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between" className="fc-event-title-container">
          <div className="fc-event-title fc-sticky">
            {t(event.title)}
          </div>
        </Stack>
      </Box>
    );
  }

  const confirmDateAsUnavailable = (eventId: string, dateStr: string) => {
    setSelectedRoom(eventId.split('--')[0]);
    setSelectedDate(dateStr);
    confirmAsUnavailable.onTrue();
  }

  const confirmDateAsAvailable = (eventId: string, dateStr: string) => {
    setSelectedRoom(eventId.split('--')[0]);
    setSelectedDate(dateStr);
    confirmAsAvailable.onTrue();
  }

  const setSelectedDateAsUnavailable = async () => {
    if (selectedRoom && selectedDate) {
      try {
        await markDateAsUnavailable(activePlace, selectedRoom, selectedDate);
        setSelectedRoom(undefined);
        setSelectedDate(undefined);
        enqueueSnackbar(t('Date marked as unavailable'), { variant: 'success' });
      } catch (error) {
        enqueueSnackbar(error, { variant: 'error' });
        console.error(error);
      }
    }
    confirmAsUnavailable.onFalse();
  }

  const setSelectedDateAsAvailable = async () => {
    if (selectedRoom && selectedDate) {
      try {
        await markDateAsAvailable(activePlace, selectedRoom, selectedDate);
        setSelectedRoom(undefined);
        setSelectedDate(undefined);
        enqueueSnackbar(t('Date marked as available'), { variant: 'success' });
      } catch (error) {
        enqueueSnackbar(error, { variant: 'error' });
        console.error(error);
      }
    }
    confirmAsAvailable.onFalse();
  }

  const toggleDateAvailability = (args: EventClickArg) => {
    console.log('calendar click:', args.event);
    if (args.event.durationEditable) {
      confirmDateAsUnavailable(args.event.id, args.event.startStr);
    }

    if (args.event.display === 'background') {
      confirmDateAsAvailable(args.event.id, args.event.startStr);
    }
  }

  useEffect(() => {
    if (calendar) {
      setMonth(`${paddedMonth(date)}-${date.getFullYear()}`);
    }
  }, [calendar, date]);

  useEffect(() => {
    onInitialView();
  }, [onInitialView]);

  return (
    <Container maxWidth="lg">
      <CustomBreadcrumbs
        heading={t("Calendar")}
        links={[
          { name: t('Home'), href: paths.owner.root },
          { name: t('Calendar') }
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }} />

      <Card sx={{ p: 3 }}>

        <Box display="flex" flexDirection="column" rowGap={3}>

          <OwnerPlaceSelect
            activePlace={activePlace}
            setActivePlace={setActivePlace}
            places={authUser?.places || []} />

          <Divider sx={{ borderStyle: 'dashed' }} />

          <StyledCalendar>
            <CalendarToolbar
              date={date}
              loading={calendarLoading}
              onNextDate={onDateNext}
              onPrevDate={onDatePrev}
              disableLogistics
              monthlyView
              alignment="left"
              flushTop
            />
            <Calendar
              weekends
              // selectable
              nowIndicator
              // selectMirror
              rerenderDelay={20}
              ref={calendarRef}
              dayMaxEventRows={3}
              initialDate={date}
              initialView={view}
              headerToolbar={false}
              events={calendar}
              eventOverlap={false}
              height="auto"
              plugins={[dayGridPlugin]}
              locale={i18n.language === 'en' ? enLocale : ptLocale}
              schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
              dayCellContent={(args) => {
                const dayEvents = calendar?.filter((event: BookingEvent) =>
                  new Date(event.start).toDateString() === args.date.toDateString()
                );

                const prices = dayEvents?.map((event: BookingEvent) => event.extendedProps?.price).filter(Boolean);
                const tooltipContent = `${prices?.map((price: number) => fCurrency(price)).join(', ')}`;

                return (
                  <Tooltip title={tooltipContent} arrow placement="top">
                    <Box>
                      {args.dayNumberText}
                    </Box>
                  </Tooltip>
                );
              }}
              dayCellClassNames="day-cell"
              eventContent={renderEventContent}
              select={(args: DateSelectArg) => {
                console.log('calendar select', args);
              }}
              eventClick={toggleDateAvailability}
              eventResize={(args: EventResizeDoneArg) => {
                console.log('calendar resize:', args.event);
              }}
            />
          </StyledCalendar>

          {calendarError && <pre><Iconify icon="mdi:alert-circle-outline" color='warning.main' /> {calendarError.message}</pre>}

        </Box>

      </Card>

      <ConfirmDialog
        open={confirmAsUnavailable.value}
        onClose={confirmAsUnavailable.onFalse}
        title={t('Mark as Unavailable')}
        content={t('Are you sure want to mark the date {{date}} as unavailable?', { date: fDate(selectedDate) })}
        action={
          <Button variant="contained" color="error" onClick={setSelectedDateAsUnavailable}>
            {t("Confirm")}
          </Button>
        } />

      <ConfirmDialog
        open={confirmAsAvailable.value}
        onClose={confirmAsAvailable.onFalse}
        title={t('Mark as Available')}
        content={t('Are you sure want to mark the date {{date}} as available?', { date: fDate(selectedDate) })}
        action={
          <Button variant="contained" color="error" onClick={setSelectedDateAsAvailable}>
            {t("Confirm")}
          </Button>
        } />
    </Container>
  );
}
