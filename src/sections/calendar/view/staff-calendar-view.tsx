'use client';

import Calendar from '@fullcalendar/react'; // => request placed at the top
import { useEffect } from 'react';
import dayGridPlugin from '@fullcalendar/daygrid';
import ptLocale from '@fullcalendar/core/locales/pt'

import { Box } from '@mui/system';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import { useGetMyCalendar } from 'src/api/user';

import { useSettingsContext } from 'src/components/settings';

import { useCalendar } from '../hooks';
import { StyledCalendar } from '../styles';
import CalendarToolbar from '../calendar-toolbar';

// ----------------------------------------------------------------------

export default function StaffCalendarView() {
  const { t } = useTranslate();
  const settings = useSettingsContext();

  const {
    date,
    calendarRef,
    onDateNext,
    onDatePrev,
    onDateToday,
    onInitialView,
  } = useCalendar('dayGridMonth');

  const { events, eventsLoading } = useGetMyCalendar(date);

  useEffect(() => {
    onInitialView();
  }, [onInitialView]);



  // const renderEventContent = (eventInfo: { event: ICalendarEvent, timeText: string }) => {
  //   const { event } = eventInfo;
  //   // console.log('@ renderEventContent() event:', event, eventInfo);

  //   return (
  //     <Stack
  //       columnGap={2}
  //       className="fc-event-main-frame"
  //       sx={{
  //         flexDirection: { xs: 'row !important', md: 'row-reverse !important' },
  //         justifyContent: { xs: 'space-between', md: 'flex-end' },
  //         alignItems: { xs: 'flex-start', md: 'center' },
  //         height: 'auto !important',
  //         p: 1
  //       }}>

  //       {event.extendedProps?.extraSheets && (
  //         <Tooltip title={t('Place with extra sheets')} placement="top" arrow>
  //           <Label
  //             variant="soft"
  //             color="error"
  //             endIcon={<Iconify icon="mdi:alert-circle" color="red" />}
  //             sx={{ verticalAlign: 'text-bottom', position: 'absolute', top: { xs: 46, md: 36 }, right: 6 }}>
  //             {t('Extra Sheets')}
  //           </Label>
  //         </Tooltip>
  //       )}

  //       {event.extendedProps?.cleaningType === JobCleaningTypes.DAILY && (
  //         <Tooltip title={t('Daily cleaning')} placement="top" arrow>
  //           <Label
  //             variant="soft"
  //             // color="primary"
  //             sx={{ verticalAlign: 'text-bottom', position: 'absolute', top: { xs: 12, md: 6 }, right: { xs: 46, md: 6 } }}>
  //             {t('Daily')}
  //           </Label>
  //         </Tooltip>
  //       )}

  //       {event.extendedProps?.cleaningType === JobCleaningTypes.DEEP && (
  //         <Tooltip title={t('Deep cleaning')} placement="top" arrow>
  //           <Label
  //             variant="soft"
  //             color="error"
  //             sx={{ verticalAlign: 'text-bottom', position: 'absolute', top: { xs: 12, md: 6 }, right: { xs: 46, md: 6 } }}>
  //             {t('Deep')}
  //           </Label>
  //         </Tooltip>
  //       )}

  //       <Stack direction="column">
  //         <div className="fc-event-time">{eventInfo.timeText}</div>
  //         <div className="fc-event-title-container">
  //           <Stack direction={{ xs: 'column', md: 'row' }} columnGap={1} className="fc-event-title fc-sticky">
  //             {event.title}
  //             <Divider orientation="vertical" flexItem />
  //             <Box sx={{ opacity: 0.75 }}>
  //               {event.extendedProps?.place?.area?.parent ? `${event.extendedProps?.place?.area?.parent?.name} / ${event.extendedProps?.place?.area?.name}` : event.extendedProps?.place?.area?.name || ''}
  //             </Box>
  //           </Stack>
  //         </div>
  //       </Stack>

  //       <Box sx={{
  //         display: 'flex',
  //         alignItems: 'center',
  //         justifyContent: 'center',
  //         width: 32,
  //         height: 32,
  //         borderWidth: 1,
  //         borderStyle: 'solid',
  //         borderRadius: '50%',
  //         position: 'relative',
  //       }}>
  //         {[JobStatus.CREATED, JobStatus.SCHEDULED, JobStatus.CANCELED, JobStatus.DELAYED].includes(String(event.extendedProps?.status)) && (
  //           <Iconify icon="ic:round-today" />
  //         )}
  //         {[JobStatus.STARTED, JobStatus.PAUSED, JobStatus.RESUMED].includes(String(event.extendedProps?.status)) && (
  //           <Iconify icon="ic:outline-timer" />
  //         )}
  //         {[JobStatus.DONE, JobStatus.INSPECTED].includes(String(event.extendedProps?.status)) && (
  //           <Iconify icon="ic:round-done-outline" />
  //         )}

  //       </Box>

  //     </Stack>
  //   );
  // }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          mb: { xs: 3, md: 5 },
        }}>
        <Typography variant="h4">
          {t('Calendar')}
        </Typography>
      </Stack>

      <Card>
        <StyledCalendar>

          <CalendarToolbar
            date={date}
            loading={eventsLoading}
            onNextDate={onDateNext}
            onPrevDate={onDatePrev}
            onToday={onDateToday}
            disableLogistics
            monthlyView
          />
          <Calendar
            weekends
            nowIndicator
            allDaySlot={false}
            rerenderDelay={20}
            ref={calendarRef}
            initialDate={date}
            initialView="dayGridMonth"
            eventDisplay="block"
            events={events}
            headerToolbar={false}
            // select={onSelectRange}
            // eventClick={(arg: EventClickArg) => {
            //   const { event } = arg;
            //   // onClickEvent();
            //   openDialog('job', event.id);
            // }}
            // eventContent={renderEventContent}
            height="auto"
            plugins={[
              dayGridPlugin,
            ]}
            locale={ptLocale}
            schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
            slotMinTime="07:00:00"
            slotMaxTime="21:00:00"
            slotMinWidth={50}
            slotDuration="00:15:00"
            slotLabelFormat={{
              hour: 'numeric',
              minute: '2-digit',
              omitZeroMinute: false,
              meridiem: 'short'
            }}
          />
        </StyledCalendar>
      </Card>
      <Stack
        direction="row"
        alignContent="center"
        alignItems="center"
        columnGap={2}
        sx={{
          mt: { xs: 1, md: 3 },
        }}>
        <Stack direction="row" gap={0.5} alignItems="baseline">
          <Box sx={{ height: 15, width: 15, bgcolor: 'green', opacity: 0.3, borderRadius: 1 }} />
          {t('Workday')}
        </Stack>

        <Stack direction="row" gap={0.5} alignItems="baseline">
          <Box sx={{ height: 15, width: 15, bgcolor: 'orange', opacity: 0.3, borderRadius: 1 }} />
          {t('Offday')}
        </Stack>

        <Stack direction="row" gap={0.5} alignItems="baseline">
          <Box sx={{ height: 15, width: 15, bgcolor: 'red', opacity: 0.3, borderRadius: 1 }} />
          {t('Vacation')}
        </Stack>

      </Stack>
    </Container>
  );
}
