import { Badge, Button } from '@mui/material';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useTranslate } from 'src/locales';
import { useGetJobsVerifiable } from 'src/api/job';

import Iconify from 'src/components/iconify';

const VerifyJobsButton = () => {
  const { t } = useTranslate();
  const { jobs } = useGetJobsVerifiable();

  return (
    <Badge color="error" badgeContent={jobs?.length}>
      <Button
        variant="soft"
        color="primary"
        size="large"
        startIcon={<Iconify icon="ph:seal-check" />}
        LinkComponent={RouterLink}
        href={paths.staff.verify}
        fullWidth>
        {t('Verify Jobs')}
      </Button>
    </Badge>
  );
}

export { VerifyJobsButton };
