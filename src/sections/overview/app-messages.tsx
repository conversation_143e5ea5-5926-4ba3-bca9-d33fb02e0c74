import Stack from '@mui/material/Stack';
import CardHeader from '@mui/material/CardHeader';
import Card, { CardProps } from '@mui/material/Card';

import { useTranslate } from 'src/locales';

// ----------------------------------------------------------------------

interface <PERSON><PERSON> extends CardProps {
  title?: string;
  subheader?: string;
  data: {
    status: string;
    quantity: number;
    value: number;
  }[];
}

export default function AppMessages({ title, subheader, data, ...other }: Props) {
  const { t } = useTranslate();
  return (
    <Card {...other} sx={{ height: '100%' }}>
      <CardHeader title={title} subheader={subheader} />

      <Stack spacing={3} sx={{ p: 3 }}>
        {t('No messages received')}
      </Stack>
    </Card>
  );
}
