import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import CardHeader from '@mui/material/CardHeader';
import Card, { CardProps } from '@mui/material/Card';
import LinearProgress from '@mui/material/LinearProgress';


// ----------------------------------------------------------------------

interface <PERSON><PERSON> extends CardProps {
  title?: string;
  subheader?: string;
  data: {
    source: string;
    quantity: number;
    value: number;
    color: string;
  }[];
}

export default function BookingSources({ title, subheader, data, ...other }: Props) {
  return (
    <Card {...other}>
      <CardHeader title={title} subheader={subheader} />

      <Stack spacing={3} sx={{ p: 3 }}>
        {data.map((item) => (
          <Stack key={item.source}>
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="space-between"
              sx={{ mb: 1 }}
            >
              <Box sx={{ typography: 'overline' }}>{item.source}</Box>
              <Box sx={{ typography: 'subtitle1' }}>{item.quantity}</Box>
            </Stack>

            <LinearProgress
              variant="determinate"
              value={item.value}
              color="primary"
              sx={{
                height: 8,
                bgcolor: (theme) => alpha(theme.palette.grey[500], 0.16),
              }}
            />
          </Stack>
        ))}
      </Stack>
    </Card>
  );
}
