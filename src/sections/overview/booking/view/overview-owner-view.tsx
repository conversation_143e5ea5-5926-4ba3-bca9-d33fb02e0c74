'use client';

import Container from '@mui/material/Container';
import Grid from '@mui/material/Unstable_Grid2';

import { useTranslate } from 'src/locales';
import { _bookings, _bookingsOverview } from 'src/_mock';

import AppMessages from '../../app-messages';
import BookingDetails from '../booking-details';
import BookingSources from '../booking-sources';
import BookingTotalIncomes from '../booking-total-incomes';
import BookingWidgetSummary from '../booking-widget-summary';

// ----------------------------------------------------------------------

const SPACING = 3;

export default function OverviewOwnerView() {
  const { t } = useTranslate();

  return (
    <Container sx={{ px: { xs: 0, md: 0 } }}>
      <Grid container spacing={SPACING} disableEqualOverflow>

        <Grid xs={12} md={4}>
          <BookingWidgetSummary
            title={t("Total Revenue")}
            total={12340}
            type='currency'
          // icon={<BookingIllustration />}
          />
        </Grid>

        <Grid xs={12} md={4}>
          <BookingWidgetSummary
            title={t("Occupancy Rate")}
            total={78}
            type='percentage'
          // icon={<CheckInIllustration />}
          />
        </Grid>

        <Grid xs={12} md={4}>
          <BookingWidgetSummary
            title={t("Average Daily Rate")}
            total={124}
            type='currency'
          // icon={<CheckOutIllustration />}
          />
        </Grid>

        <Grid container xs={12} md={12}>
          <Grid xs={12} md={4}>
            <BookingTotalIncomes
              title={t("In the last 3 months")}
              total={8765}
              percent={2.6}
              chart={{
                series: [
                  { x: '1 Janeiro', y: 936 },
                  { x: '15 Janeiro', y: 667 },
                  { x: '1 Fevereiro', y: 540 },
                  { x: '15 Fevereiro', y: 720 },
                  { x: '1 Março', y: 840 },
                  { x: '15 Março', y: 1110 },
                ],
              }}
            />
          </Grid>

          <Grid xs={12} md={4}>
            <BookingSources
              title={t("Booking Sources")}
              data={[
                {
                  "source": "Airbnb",
                  "quantity": 11,
                  "value": 20.1,
                  "color": "#ff5a5f"
                },
                {
                  "source": "Booking.com",
                  "quantity": 19,
                  "value": 13.6,
                  "color": "#003580"
                },
                {
                  "source": "Vrbo",
                  "quantity": 91,
                  "value": 38.2,
                  "color": "#0E214B"
                }
              ]}
            />
          </Grid>

          <Grid xs={12} md={4}>
            <AppMessages
              title={t("Messages")}
              data={_bookingsOverview} />
          </Grid>
        </Grid>

        <Grid xs={12}>
          <BookingDetails
            title={t("Upcoming Bookings")}
            tableData={_bookings}
            tableLabels={[
              { id: 'id', label: t('ID') },
              { id: 'customer', label: t('Guest') },
              { id: 'checkIn', label: t('Check In') },
              { id: 'checkOut', label: t('Check Out') },
              { id: 'status', label: t('Status') },
              { id: '' },
            ]}
          />
        </Grid>

      </Grid>
    </Container>
  );
}
