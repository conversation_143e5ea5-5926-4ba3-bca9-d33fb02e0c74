import * as Yup from 'yup';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MenuItem from '@mui/material/MenuItem';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useTranslate } from 'src/locales';
import { PLACE_STATUS, updatePlaces, PLACE_STATUS_OPTIONS } from 'src/api/place';

import { useSnackbar } from 'src/components/snackbar';
import FormProvider, { RHFSelect } from 'src/components/hook-form';

import { IPlace, IPlacesUpdate } from 'src/types/place';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  selectedPlaces?: string[];
  onUpdate?: (data: IPlace) => void;
};

export default function PlaceMultiEditForm({ selectedPlaces, open, onClose, onUpdate }: Props) {
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslate();

  const PlacesSchema = Yup.object().shape({
    status: Yup.string().required(t('Status is required')),
  });

  const defaultValues = useMemo(
    () => ({
      status: PLACE_STATUS.READY,
    }),
    []
  );

  const methods = useForm({
    resolver: yupResolver(PlacesSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    if (!selectedPlaces || !selectedPlaces.length) return;

    try {
      const updateDto: IPlacesUpdate = {
        ids: selectedPlaces,
        status: data.status,
      };

      const updateRequest = await updatePlaces(updateDto);

      onUpdate?.(updateRequest.data);
      onClose();
      enqueueSnackbar(t('Places updated'));
    } catch (error) {
      enqueueSnackbar(t(error.message || error) || t('Could not update the places'), { variant: 'error' });
      console.error(error);
    }
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 400 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>{t('Edit {{total}} Places', { total: selectedPlaces?.length })}</DialogTitle>

        <DialogContent>

          <Box
            rowGap={3}
            columnGap={2}
            display="grid"
            sx={{ mb: 3, mt: 1 }}>

            <RHFSelect name="status" label={t("Status")}>
              {PLACE_STATUS_OPTIONS.map((status) => (
                <MenuItem key={status.value} value={status.value}>
                  {status.label}
                </MenuItem>
              ))}
            </RHFSelect>

          </Box>

        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            {t("Cancel")}
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
            {t("Update")}
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
