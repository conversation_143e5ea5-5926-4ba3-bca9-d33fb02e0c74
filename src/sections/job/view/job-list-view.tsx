'use client';

import isEqual from 'lodash/isEqual';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { Stack } from '@mui/system';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import { LoadingButton } from '@mui/lab';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import { Tab, Fab, Tabs, alpha, Tooltip } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDate, fTime } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { useGetPlaces } from 'src/api/place';
import { useAuthContext } from 'src/auth/hooks';
import { USER_ROLE, useGetStaff } from 'src/api/user';
import { JobStatus, deleteJob, deleteJobs, useGetPaginatedJobs, useGetJobsStatusCounts } from 'src/api/job';

import Label from 'src/components/label';
import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useSnackbar } from 'src/components/snackbar';
import { useSettingsContext } from 'src/components/settings';
import { ConfirmDialog } from 'src/components/custom-dialog';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  TableNoData,
  getComparator,
  exportToExcel,
  TableSkeleton,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import AuditEntityDialog from 'src/sections/auditlog/auditlog-entity-dialog';

import { AuditlogEntityType } from 'src/types/auditlog';
import { IJob, IJobTableFilters, IJobTableFilterValue } from 'src/types/job';

import JobTableRow from '../job-table-row';
import JobNewEditForm from '../job-new-edit-form';
import JobTableToolbar from '../job-table-toolbar';
import JobTimelogsDialog from '../job-timelogs-dialog';

// ----------------------------------------------------------------------

const defaultFilters: IJobTableFilters = {
  search: '',
  place: [],
  user: [],
  start: '',
  status: [],
  tab: 'all',
};

const JobTab = {
  CREATED: 'created',
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  DELTA_FLAGGED: 'delta_flagged',
};

// ----------------------------------------------------------------------

export default function JobListView() {
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslate();
  const settings = useSettingsContext();
  const confirm = useBoolean();
  const { user: authUser } = useAuthContext();

  const table = useTable({
    defaultRowsPerPage: settings.tableRowsPerPage || 10,
    defaultOrderBy: 'start',
    defaultOrder: 'desc',
  });

  const [tableData, setTableData] = useState<IJob[]>([]);
  const [filters, setFilters] = useState(defaultFilters);

  const { counts, countsMutate } = useGetJobsStatusCounts();
  const { places } = useGetPlaces();
  const { users: staff } = useGetStaff();

  const { jobs, jobsLoading, jobsTotalItems, jobsMutate } = useGetPaginatedJobs({
    page: table.page + 1,
    limit: table.rowsPerPage,
    sortBy: `${table.orderBy}:${table.order.toUpperCase()}`,
    search: filters.search,
    filter: {
      ...(filters.start && { start: filters.start }),
      ...(filters.place.length && { place: filters.place }),
      ...(filters.user.length && { user: filters.user }),
      ...(filters.status.length && { status: filters.status }),
      ...(filters.tab === JobTab.DELTA_FLAGGED && { delta_flagged: true }),
    },
  });

  const formOpen = useBoolean();
  const timelogsOpen = useBoolean();
  const auditList = useBoolean();
  const [currentJob, setCurrentJob] = useState<IJob>();
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    if (jobs) {
      setTableData(jobs);
    }
  }, [jobs, table.page, jobsTotalItems]);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const dataInPage = dataFiltered.slice(
    table.page * table.rowsPerPage,
    table.page * table.rowsPerPage + table.rowsPerPage
  );

  const canReset = !isEqual(defaultFilters, filters);

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const isDashUser = [USER_ROLE.admin, USER_ROLE.manager].includes(authUser?.role);

  const tabs = useMemo(() => [
    { value: 'all', label: t('All') },
    {
      value: JobTab.CREATED,
      label: t('Created'),
      options: [
        JobStatus.CREATED,
      ]
    },
    {
      value: JobTab.SCHEDULED,
      label: t('Scheduled'),
      options: [
        JobStatus.SCHEDULED,
      ]
    },
    {
      value: JobTab.IN_PROGRESS,
      label: t('In Progress'),
      options: [
        JobStatus.STARTED,
        JobStatus.PAUSED,
        JobStatus.RESUMED,
        JobStatus.DELAYED,
      ]
    },
    {
      value: JobTab.COMPLETED,
      label: t('Done'),
      options: [
        JobStatus.DONE,
        JobStatus.INSPECTED,
        JobStatus.CANCELED,
      ]
    },
    {
      value: JobTab.DELTA_FLAGGED,
      label: t('Delta Flagged'),
      options: []
    }
  ], [t]);

  const getStatusOptions = useCallback((tab: string) => tabs.find((tabItem) => tabItem.value === tab)?.options || [], [tabs]);

  const handleFilters = useCallback(
    (name: string, value: IJobTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  // const handleResetFilters = useCallback(() => {
  //   setFilters(defaultFilters);
  // }, []);

  const handleDeleteRow = useCallback(
    async (id: string) => {

      try {
        await deleteJob(id);

        const deleteRow = tableData.filter((row) => row.id !== id);
        enqueueSnackbar(t('Job removed'));
        setTableData(deleteRow);
        table.onUpdatePageDeleteRow(dataInPage.length);
        jobsMutate();
        countsMutate();
      } catch (error) {
        enqueueSnackbar(t(error.message || error), { variant: 'error' });
        console.error(error);
      }
    },
    [dataInPage.length, enqueueSnackbar, table, tableData, t, jobsMutate, countsMutate]
  );

  const handleDeleteRows = useCallback(async () => {
    try {
      const deleteRows = tableData.filter((row) => !table.selected.includes(row.id));

      const jobIds = table.selected;
      await deleteJobs(jobIds);

      enqueueSnackbar(t('Jobs removed'));
      setTableData(deleteRows);
      table.onUpdatePageDeleteRows({
        totalRowsInPage: dataInPage.length,
        totalRowsFiltered: dataFiltered.length,
      });
      jobsMutate();
      countsMutate();
    } catch (error) {
      enqueueSnackbar(t(error.message || error), { variant: 'error' });
      console.error(error);
    }
  }, [dataFiltered.length, dataInPage.length, enqueueSnackbar, table, tableData, t, jobsMutate, countsMutate]);

  const handleExportRows = useCallback(() => {
    setExporting(true);
    const dataToExport = tableData.filter((row) => table.selected.includes(row.id)).map((row) => ({
      [t('ID')]: row.id,
      [t('Date')]: `${fDate(row.start, 'dd/MM/yyyy')}`,
      [t('Start/End')]: `${fTime(row.start)} - ${fTime(row.end)}`,
      [t('User')]: `${row.user?.firstName} ${row.user?.lastName}`,
      [t('Place')]: row.place?.title,
      [t('Hours')]: row.loggedHours,
      [t('Timelogs')]: row.timelogs?.length,
      [t('Status')]: t(row.status),
      [t('Updated')]: row.updated,
    }));
    exportToExcel(dataToExport, t('jobs'));
    setExporting(false);
  }, [table, tableData, t]);

  const handleEditRow = (id: string) => {
    setCurrentJob(tableData.find((row) => row.id === id));
    formOpen.onTrue();
  };

  const handleListTimelogs = (id: string) => {
    setCurrentJob(tableData.find((row) => row.id === id));
    timelogsOpen.onTrue();
  };

  const handleUpdateRow = (updatedRow: IJob) => {
    const updateRow = tableData.map((row) => (row.id === updatedRow.id ? updatedRow : row));
    setTableData(updateRow);
  };

  const handleFilterTab = useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      handleFilters('tab', newValue);
      handleFilters('status', getStatusOptions(newValue));
    },
    [getStatusOptions, handleFilters]
  );

  // const handleResetFilters = useCallback(() => {
  //   setFilters(defaultFilters);
  // }, []);

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <CustomBreadcrumbs
          heading={t("Jobs")}
          links={isDashUser ? [
            { name: t('Dashboard'), href: paths.dashboard.root },
            { name: t('Jobs') }
          ] : []}
          action={
            <>
              <Button
                onClick={() => {
                  setCurrentJob(undefined);
                  formOpen.onTrue()
                }}
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                sx={{ display: { xs: 'none', sm: 'inline-flex' } }}>
                {t("Add Job")}
              </Button>
              <Fab
                variant="circular"
                size="small"
                onClick={() => {
                  setCurrentJob(undefined);
                  formOpen.onTrue()
                }}
                sx={{ display: { xs: 'inline-flex', sm: 'none' } }}>
                <Iconify icon="mingcute:add-line" />
              </Fab>
            </>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }} />

        <Card>
          <Tabs
            value={filters.tab}
            onChange={handleFilterTab}
            sx={{
              px: { xs: 1, md: 2.5 },
              boxShadow: (theme) => `inset 0 -2px 0 0 ${alpha(theme.palette.grey[500], 0.08)}`,
            }}>
            {tabs.map((tab) => (
              <Tab
                key={tab.value}
                iconPosition="end"
                value={tab.value}
                label={t(tab.label)}
                icon={
                  <Label
                    variant={
                      ((tab.value === 'all' || tab.value === filters.tab) && 'filled') || 'soft'
                    }
                    color={
                      (tab.value === JobTab.COMPLETED && 'success') ||
                      (tab.value === JobTab.IN_PROGRESS && 'warning') ||
                      (tab.value === JobTab.SCHEDULED && 'info') ||
                      (tab.value === JobTab.DELTA_FLAGGED && 'secondary') ||
                      'default'
                    }>
                    {counts?.[tab.value] || 0}
                  </Label>
                } />
            ))}
          </Tabs>

          <JobTableToolbar
            filters={filters}
            onFilters={handleFilters} />

          {/* {canReset && (
            <JobTableFiltersResult
              filters={filters}
              onFilters={handleFilters}
              onResetFilters={handleResetFilters}
              results={dataFiltered.length}
              sx={{ p: 2.5, pt: 0 }}
            />
          )} */}

          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <TableSelectedAction
              dense={table.dense}
              numSelected={table.selected.length}
              rowCount={dataFiltered.length}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  dataFiltered.map((row) => row.id)
                )
              }
              action={
                <Stack direction="row" spacing={2}>
                  <Tooltip title={t('Export')}>
                    <LoadingButton loading={exporting} color="primary" onClick={handleExportRows} variant="outlined" startIcon={<Iconify icon="mdi:export-variant" />}>
                      {t("Export")}
                    </LoadingButton>
                  </Tooltip>
                  <Tooltip title={t('Remove')}>
                    <Button color="primary" onClick={confirm.onTrue} variant="outlined" startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}>
                      {t("Remove")}
                    </Button>
                  </Tooltip>
                </Stack>
              }
            />

            <Scrollbar>
              <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={[
                    { id: 'start', label: t('Date') },
                    { id: 'time', label: t('Start/End') },
                    { id: 'user', label: t('User') },
                    { id: 'place', label: t('Place') },
                    { id: 'logged', label: t('Hours') },
                    { id: 'timelogs', label: t('Timelogs') },
                    { id: 'status', label: t('Status') },
                    { id: 'updated', label: t('Updated'), width: 180 },
                    { id: '', width: 88 },
                  ]}
                  rowCount={dataFiltered.length}
                  numSelected={table.selected.length}
                  onSort={table.onSort}
                  onSelectAllRows={(checked) =>
                    table.onSelectAllRows(
                      checked,
                      dataFiltered.map((row) => row.id)
                    )
                  }
                />

                <TableBody>
                  {dataFiltered
                    .map((row) => (
                      <JobTableRow
                        key={row.id}
                        row={row}
                        selected={table.selected.includes(row.id)}
                        onSelectRow={() => table.onSelectRow(row.id)}
                        onDeleteRow={() => handleDeleteRow(row.id)}
                        onEditRow={() => handleEditRow(row.id)}
                        onShowTimelogs={() => handleListTimelogs(row.id)}
                        onShowAudit={() => {
                          setCurrentJob(row);
                          auditList.onTrue();
                        }}
                      />
                    ))}

                  {jobsLoading && (
                    <TableSkeleton rows={10} sx={{ height: 76 }} />
                  )}

                  {!jobsLoading && <TableNoData notFound={notFound} />}

                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          {!notFound && !jobsLoading && (
            <TablePaginationCustom
              count={jobsTotalItems}
              page={!jobsTotalItems || jobsTotalItems <= 0 ? 0 : table.page}
              rowsPerPage={table.rowsPerPage}
              onPageChange={table.onChangePage}
              onRowsPerPageChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                table.onChangeRowsPerPage(event);
                settings.onUpdate('tableRowsPerPage', event.target.value);
              }} />
          )}

        </Card>
      </Container>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={t('Remove')}
        content={
          <>
            {
              table.selected.length === 1 ?
                t("Are you sure want to remove 1 job?") :
                t("Are you sure want to remove {{total}} jobs?", { total: table.selected.length })
            }
          </>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              handleDeleteRows();
              confirm.onFalse();
            }}>
            {t('Remove')}
          </Button>
        }
      />

      <JobNewEditForm
        currentJob={currentJob}
        places={places}
        staff={staff}
        open={formOpen.value}
        onClose={formOpen.onFalse}
        onUpdate={handleUpdateRow} />

      {currentJob && (
        <JobTimelogsDialog
          job={currentJob}
          open={timelogsOpen.value}
          onClose={timelogsOpen.onFalse} />
      )}

      {currentJob && (
        <AuditEntityDialog
          entityId={currentJob.id}
          entityType={AuditlogEntityType.JOB}
          entity={currentJob}
          open={auditList.value}
          onClose={() => {
            auditList.onFalse();
            setCurrentJob(undefined);
          }} />
      )}
    </>
  );
}

// ----------------------------------------------------------------------

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: IJob[];
  comparator: (a: any, b: any) => number;
  filters: IJobTableFilters;
}) {
  // const { name, date, status, user, place, tab } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index] as const);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  // if (name) {
  //   inputData = inputData.filter(
  //     (job) => (job.place && job.place.title?.toLowerCase().indexOf(name.toLowerCase()) !== -1) || (job.user && job.user.firstName?.toLowerCase().indexOf(name.toLowerCase()) !== -1)
  //   );
  // }

  // if (tab === JobTab.DELTA_FLAGGED) {
  //   inputData = inputData.filter((job) => !!job.delta);
  // }

  // if (date) {
  //   inputData = inputData.filter(
  //     (job) => fDate(job.start, 'dd/MM/yyyy') === fDate(date, 'dd/MM/yyyy')
  //   );
  // }

  // if (status.length) {
  //   inputData = inputData.filter((job) => status.includes(job.status));
  // }

  // if (user.length) {
  //   inputData = inputData.filter((job) => job.user && user.includes(job.user.id));
  // }

  // if (place.length) {
  //   inputData = inputData.filter((job) => job.place && place.includes(job.place.id));
  // }

  return inputData;
}
