'use client';

import { mutate } from 'swr';
import { useState, useEffect } from 'react';

import { Box } from '@mui/system';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { Button, Divider, IconButton } from '@mui/material';

import { useRouter, usePathname } from 'src/routes/hooks';

import { useBoolean } from 'src/hooks/use-boolean';

import { endpoints } from 'src/utils/axios';
import { fDateTime } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { JobStatus, markJobAsVerified, useGetJobsVerifiable } from 'src/api/job';

import Iconify from 'src/components/iconify';
import { JobStatusIcon } from 'src/components/icons';
import { useSnackbar } from 'src/components/snackbar';
import { useSettingsContext } from 'src/components/settings';
import { ConfirmDialog } from 'src/components/custom-dialog';

import ErrorBoundary from 'src/sections/error/error-boundary';
import PlaceSummaryCard from 'src/sections/place/place-summary-card';
import StaffQueryDialog from 'src/sections/staff/staff-query-dialog/staff-query-dialog';

import { IJob } from 'src/types/job';
import { IPlace } from 'src/types/place';

// ----------------------------------------------------------------------


// ----------------------------------------------------------------------

export default function JobVerifyListView() {
  const router = useRouter();
  const pathname = usePathname();
  const { enqueueSnackbar } = useSnackbar();

  const { t } = useTranslate();
  const settings = useSettingsContext();

  const { jobs, jobsLoading } = useGetJobsVerifiable();
  const [currentJob, setCurrentJob] = useState<IJob>();
  const [verifiableJobs, setVerifiableJobs] = useState<IJob[]>([]);
  const confirmationOpen = useBoolean();

  const openDialog = (type: 'job' | 'place' | 'bag' | 'asset' | 'tag', id: string) => {
    const query = new URLSearchParams();
    query.delete('job');
    query.delete('place');
    query.delete('bag');
    query.delete('asset');
    query.set(type, id);
    router.push(`${pathname}?${query.toString()}`);
  }

  const markCurrentJobAsVerified = async () => {
    if (!currentJob) return;

    try {
      await markJobAsVerified(currentJob.id);
      setVerifiableJobs(verifiableJobs.filter(job => job.id !== currentJob.id));
      mutate(endpoints.job.verifiable);
      enqueueSnackbar(t('Job {{title}} updated', { title: currentJob.title }));
      setCurrentJob(undefined);
    } catch (error) {
      console.error(error);
      enqueueSnackbar(
        t('Could not update job {{title}}', { title: currentJob.title, variant: 'error' })
      );
    }
  }

  useEffect(() => {
    if (jobs) {
      setVerifiableJobs(jobs);
    }
  }, [jobs]);

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            mb: { xs: 3, md: 5 },
          }}>
          <Typography variant="h4">
            {t('Verify Jobs')}
          </Typography>
        </Stack>

        <Stack spacing={2}>
          {jobsLoading && (
            <Card sx={{ p: 2 }}>
              <Typography variant="body2" sx={{ p: 3 }}>
                {t('Loading...')}
              </Typography>
            </Card>
          )}

          {verifiableJobs?.length === 0 && (
            <Card sx={{ p: 3 }}>
              <Stack direction="column" alignItems="center" gap={1}>
                <Iconify icon="ph:seal-check" width={32} />
                <Typography variant="body2">
                  {t('No jobs to verify')}
                </Typography>
              </Stack>
            </Card>
          )}

          {verifiableJobs?.map((job) => (
            <Card key={job.id} sx={{ p: 2 }}>
              <Box display="flex" flexDirection="column" rowGap={2} sx={{ flexGrow: 1, position: 'relative' }}>
                <PlaceSummaryCard place={job.place as IPlace} showStatus />
                <IconButton
                  onClick={() => openDialog('job', job.id)}
                  sx={{ position: 'absolute', top: -6, right: -6 }}>
                  <Iconify icon="material-symbols:frame-inspect-rounded" />
                </IconButton>
                <Divider sx={{ borderStyle: 'dashed' }} />

                <Stack direction="row" justifyContent="space-between" color='text.secondary'>
                  <Typography variant="subtitle2" display="flex" alignItems="center" columnGap={1}>
                    <JobStatusIcon status={job.status} />
                    {t(`job.${job.status}`)}
                  </Typography>
                  <Typography variant="subtitle2" sx={{ textTransform: 'capitalize' }}>
                    {fDateTime(job.start)}
                  </Typography>
                </Stack>
                <Divider sx={{ borderStyle: 'dashed' }} />

                {job.cleaningVerified && (
                  <>
                    <Stack direction="row" justifyContent="space-between" color='text.secondary'>
                      <Typography variant="subtitle2" display="flex" alignItems="center" columnGap={1}>
                        <Iconify icon="ph:seal-check-fill" />
                        {t('Verified on {{date}} by {{name}}', {
                          date: new Date(job.cleaningVerified).toLocaleDateString(),
                          name: job.cleaningVerifiedBy ? job.cleaningVerifiedBy.firstName : t('Unknown'),
                        })}
                      </Typography>
                    </Stack>
                    <Divider sx={{ borderStyle: 'dashed' }} />
                  </>
                )}

                <Button
                  variant="soft"
                  color='success'
                  size="large"
                  startIcon={<Iconify icon="ph:seal-check-bold" />}
                  disabled={job.status !== JobStatus.DONE}
                  onClick={() => {
                    setCurrentJob(job);
                    confirmationOpen.onTrue();
                  }}>
                  {t('Mark as verified')}
                </Button>
              </Box>
            </Card>
          ))}

        </Stack>
      </Container>

      <ErrorBoundary>
        <StaffQueryDialog />
      </ErrorBoundary>

      <ConfirmDialog
        open={confirmationOpen.value}
        onClose={confirmationOpen.onFalse}
        title={t('Mark as verified')}
        content={t(
          "Are you sure you want to mark the job {{title}} as verified?",
          { title: currentJob?.title || currentJob?.place?.title }
        )}
        action={
          <Button
            variant="contained"
            color="success"
            onClick={() => {
              markCurrentJobAsVerified();
              confirmationOpen.onFalse();
            }}>
            {t('Yes')}
          </Button>
        }
      />

    </>
  );
}
