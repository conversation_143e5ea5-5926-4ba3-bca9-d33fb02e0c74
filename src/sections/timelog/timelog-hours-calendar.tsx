import Calendar from '@fullcalendar/react';
import { useState, useEffect } from 'react';
import dayGridPlugin from '@fullcalendar/daygrid';
import ptLocale from '@fullcalendar/core/locales/pt'

import { Box, Stack } from '@mui/system';
import { Dialog, Avatar, Button, Divider, Tooltip, IconButton, Typography, DialogTitle, DialogContent } from '@mui/material';

import { fDate } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { getUserHoursCalendar } from 'src/api/user';
import { getSmallImageUrl } from 'src/config-global';

import Iconify from 'src/components/iconify';

import { IUser } from 'src/types/user';
import { ICalendarEvent } from 'src/types/calendar';

import { useCalendar } from '../calendar/hooks';
import { StyledCalendar } from '../calendar/styles';


type Props = {
  open: boolean;
  onClose: VoidFunction;
  user?: Partial<IUser>;
};

type HoursEvent = Partial<ICalendarEvent> & {
  textColor?: string;
};

const MonthHours = ({ hours }: { hours: number }) => {
  const { t } = useTranslate();

  return (
    <Box sx={{
      height: 50,
      alignContent: 'center',
      justifyItems: 'center',
      bgcolor: 'warning.main',
      color: 'warning.contrastText',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        left: 0,
        bottom: 0,
        height: '20%',
        width: '100%',
        clipPath: 'polygon(0 100%, 100% 100%, 50% 0)',
        backgroundColor: 'error.main',
        zIndex: 1
      }
    }}>
      <Tooltip title={t('This month: {{hours}} hours', { hours: hours.toFixed(2) })} placement="top" arrow>
        <Typography variant='body2'>{Math.round(hours)}h</Typography>
      </Tooltip>
    </Box>
  )
};

const WeekHours = ({ hours }: { hours: number }) => {
  const { t } = useTranslate();

  return (
    <Box sx={{
      height: 80,
      pr: '8px',
      alignContent: 'center',
      justifyItems: 'flex-end',
      bgcolor: 'warning.main',
      color: 'warning.contrastText',
      backgroundImage: 'linear-gradient(#FFD666 75%, #FFAB00)',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
        width: '20%',
        clipPath: 'polygon(0 5%, 0 95%, 100% 50%)',
        backgroundColor: 'error.main',
        zIndex: 1
      }
    }}>
      <Tooltip title={t('This week: {{hours}} hours', { hours: hours.toFixed(2) })} placement="top" arrow>
        <Typography variant='body2'>{Math.round(hours)}h</Typography>
      </Tooltip>
    </Box>
  )
};

export default function TimelogHoursCalendar({ user, open, onClose }: Props) {
  const { t } = useTranslate();

  const {
    date,
    calendarRef,
    onDatePrev,
    onDateNext,
    onInitialView
  } = useCalendar('dayGridMonth');

  const [events, setEvents] = useState<HoursEvent[]>([]);

  // Extract hours from event title (format: "8.50h", "7.25h", etc. - 2 decimal places)
  const extractHoursFromTitle = (title: string): number => {
    const match = title.match(/(\d+(?:\.\d+)?)h?/);
    return match ? parseFloat(match[1]) : 0;
  };

  // Calculate weekly hours (Monday to Sunday weeks as displayed in calendar)
  const calculateWeeklyHours = () => {
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    // Get all weeks that intersect with the current month (Monday to Sunday)
    const weeks = [];
    const currentWeekStart = new Date(monthStart);

    // Find the Monday of the week containing the first day of the month
    const dayOfWeek = currentWeekStart.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday
    currentWeekStart.setDate(currentWeekStart.getDate() - daysToSubtract);

    while (currentWeekStart <= monthEnd) {
      const weekStart = new Date(currentWeekStart);
      const weekEnd = new Date(currentWeekStart);
      weekEnd.setDate(weekEnd.getDate() + 6); // End of week (Sunday)

      weeks.push({ start: weekStart, end: weekEnd });
      currentWeekStart.setDate(currentWeekStart.getDate() + 7);
    }

    const weeklyTotals = weeks.map(week => {
      const weekEvents = events.filter(event => {
        if (!event.start) return false;

        // Extract just the date part for comparison (ignore time)
        const eventDate = new Date(event.start);
        const eventDateOnly = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
        const weekStartOnly = new Date(week.start.getFullYear(), week.start.getMonth(), week.start.getDate());
        const weekEndOnly = new Date(week.end.getFullYear(), week.end.getMonth(), week.end.getDate());

        return eventDateOnly >= weekStartOnly && eventDateOnly <= weekEndOnly;
      });

      const weekTotal = weekEvents.reduce((total, event) => total + extractHoursFromTitle(event.title || ''), 0);

      return weekTotal;
    });

    // Always return exactly 6 weeks (pad with zeros if needed)
    while (weeklyTotals.length < 6) {
      weeklyTotals.push(0);
    }

    return weeklyTotals.slice(0, 6); // Ensure we never have more than 6
  };

  const weeklyHours = calculateWeeklyHours();

  // Calculate total month hours
  const totalMonthHours = weeklyHours.reduce((total, hours) => total + hours, 0);

  useEffect(() => {
    const loadCalendar = async () => {
      const calendar = await getUserHoursCalendar(user?.id || '', date);
      setEvents(calendar.data.map((event: HoursEvent) => ({
        ...event,
        textColor: '#FF0000',
      })));
    };

    if (user && open) {
      loadCalendar();
    }

  }, [user, date, open]);


  const renderEventContent = (eventInfo: { event: ICalendarEvent, timeText: string }) => {
    const { event } = eventInfo;
    const hoursEvent = event as HoursEvent;

    return (
      <Box className="fc-event-main-frame">
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between" className="fc-event-title-container">
          <div className="fc-event-title fc-sticky">
            {hoursEvent.title}
          </div>
        </Stack>
      </Box>
    );
  }

  useEffect(() => {
    onInitialView();
  }, [onInitialView]);

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}>
      <DialogTitle>
        <Stack direction="row" columnGap={2} alignItems="center">
          {user?.photo && (
            <Avatar alt={`${user.firstName} ${user.lastName}`} src={getSmallImageUrl(user.photo)} sx={{ my: -2 }} />
          )}
          {`${user?.firstName} ${user?.lastName}`}
          <Divider orientation="vertical" flexItem />
          {t('Hours Calendar')}
        </Stack>
        <IconButton onClick={onClose} sx={{ position: 'absolute', top: 20, right: 12 }}>
          <Iconify icon="mdi:close" />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3, pb: 3, overflow: 'hidden' }}>

        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ textTransform: 'capitalize' }}>
            {fDate(date, 'MMMM yyyy')}
          </Typography>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Button variant="soft" onClick={onDatePrev} startIcon={<Iconify icon="eva:arrow-ios-back-fill" />}>
              {t('Previous')}
            </Button>

            {/* <Divider orientation="vertical" flexItem sx={{ mx: 1 }} /> */}

            <Button variant="soft" onClick={onDateNext} endIcon={<Iconify icon="eva:arrow-ios-forward-fill" />}>
              {t('Next')}
            </Button>
          </Stack>
        </Stack>

        <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
          <StyledCalendar sx={{ '.fc-today-button': { display: { xs: 'none', md: 'inline-block' } } }}>
            <Calendar
              weekends
              rerenderDelay={20}
              ref={calendarRef}
              dayMaxEventRows={1}
              initialDate={date}
              initialView="dayGridMonth"
              events={events}
              eventDisplay="block"
              eventContent={renderEventContent}
              headerToolbar={false}
              eventOverlap={false}
              height="auto"
              plugins={[dayGridPlugin]}
              locale={ptLocale}
              schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
            />
          </StyledCalendar>

          <Stack direction="column" sx={{ width: { xs: 55, md: 65 } }}>

            <MonthHours hours={totalMonthHours} />

            {weeklyHours.map((hours, index) => (
              <WeekHours key={index} hours={hours} />
            ))}

          </Stack>

        </Stack>
      </DialogContent>
    </Dialog>
  );
}
