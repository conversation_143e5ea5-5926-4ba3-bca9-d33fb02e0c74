import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';

import { useBoolean } from 'src/hooks/use-boolean';

import { useTranslate } from 'src/locales';

import Iconify from 'src/components/iconify';

import { IStaffHours } from 'src/types/timelog';

import TimelogHoursCalendar from './timelog-hours-calendar';



// ----------------------------------------------------------------------

type Props = {
  selected: boolean;
  row: IStaffHours;
  onSelectRow: VoidFunction;
};

export default function TimelogTableRow({
  row,
  selected,
  onSelectRow,
}: Props) {
  const { user, scheduledHours, loggedHours, difference, jobCount } = row;

  const { t } = useTranslate();
  const hoursCal = useBoolean();

  return (
    <>
      <TableRow hover selected={selected}>
        <TableCell padding="checkbox">
          <Checkbox checked={selected} onClick={onSelectRow} />
        </TableCell>

        <TableCell sx={{ textTransform: 'capitalize' }}>
          {user ? t(`${user.firstName} ${user.lastName}`) : t('Undefined')}
        </TableCell>

        <TableCell>
          {scheduledHours} h
        </TableCell>

        <TableCell>
          {loggedHours} h
        </TableCell>

        <TableCell>
          <Tooltip title={`${Math.round(difference * 60)} ${(Math.round(difference * 60) === 1 ? t('minute') : t('minutes'))}`} placement="top" arrow>
            <span>{difference} h</span>
          </Tooltip>
        </TableCell>

        <TableCell>
          {jobCount}
        </TableCell>


        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title={t("View hours calendar")} placement="top" arrow>
            <IconButton onClick={() => {
              hoursCal.onTrue();
            }}>
              <Iconify icon="material-symbols:calendar-clock-rounded" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <TimelogHoursCalendar
        user={user}
        open={hoursCal.value}
        onClose={hoursCal.onFalse} />
    </>
  );
}
