import { useMemo, useState, useEffect } from 'react';

import { Stack } from '@mui/system';
import { Table, Dialog, Divider, Tooltip, TableRow, TableBody, TableCell, IconButton, DialogTitle, DialogContent, TableContainer } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDate, fTime } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { useGetAuditlogsForEntity } from 'src/api/auditlog';

import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useTable, emptyRows, TableNoData, TableEmptyRows, TableHeadCustom, TablePaginationCustom } from 'src/components/table';

import { IBag } from 'src/types/bag';
import { IJob } from 'src/types/job';
import { ITag } from 'src/types/tag';
import { IUser } from 'src/types/user';
import { IArea } from 'src/types/area';
import { IPlace } from 'src/types/place';
import { IAsset } from 'src/types/asset';
import { IVehicle } from 'src/types/vehicle';
import { IIncident } from 'src/types/incident';
import { ITypology } from 'src/types/typology';
import { IAuditlog, AuditlogEntityType } from 'src/types/auditlog';


interface Props {
  open: boolean;
  onClose: () => void;
  entityType: string;
  entityId: string;
  entity?: IPlace | IUser | IJob | IVehicle | IAsset | IBag | ITag | IIncident | IArea | ITypology | null;
}

export default function AuditEntityDialog({ open, onClose, entityType, entityId, entity }: Props) {
  const { t } = useTranslate();
  const { auditlogs } = useGetAuditlogsForEntity(entityType, entityId);

  const table = useTable({ defaultRowsPerPage: 10, defaultOrderBy: 'date' });
  const [tableData, setTableData] = useState<IAuditlog[]>([]);
  const [currentDiff, setCurrentDiff] = useState<any>({});
  const diffOpen = useBoolean();

  useEffect(() => {
    if (auditlogs) {
      setTableData(auditlogs);
    }
  }, [auditlogs]);

  const dataFiltered = tableData;
  // const dataInPage = tableData.slice(
  //   table.page * table.rowsPerPage,
  //   table.page * table.rowsPerPage + table.rowsPerPage
  // );

  const denseHeight = table.dense ? 56 : 56 + 20;

  const notFound = !dataFiltered.length;

  const entityName = useMemo(() => {
    switch (entityType) {
      case AuditlogEntityType.PLACE:
        return (entity as IPlace).title;
      case AuditlogEntityType.USER:
        return `${(entity as IUser).firstName} ${(entity as IUser).lastName}`;
      case AuditlogEntityType.JOB:
        return (entity as IJob).place?.title || (entity as IJob).title;
      case AuditlogEntityType.VEHICLE:
        return (entity as IVehicle).name;
      case AuditlogEntityType.ASSET:
        return (entity as IAsset).name;
      case AuditlogEntityType.TAG:
        return (entity as ITag).serial;
      case AuditlogEntityType.INCIDENT:
        return (entity as IIncident).title;
      case AuditlogEntityType.BAG:
        return (entity as IBag).tag.serial;
      case AuditlogEntityType.AREA:
        return (entity as IArea).name;
      case AuditlogEntityType.TYPOLOGY:
        return (entity as ITypology).name;
      default:
        return '';
    }
  }, [entityType, entity]);

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 960 },
      }}>
      <DialogTitle>
        <Stack direction="row" columnGap={2} alignItems="center">
          {entityName}
          <Divider orientation="vertical" flexItem />
          {t(`entity.${entityType}`)}{' '}
          <Divider orientation="vertical" flexItem />
          {t('Audit Logs')}
        </Stack>
        <IconButton onClick={onClose} sx={{ position: 'absolute', top: 20, right: 12 }}>
          <Iconify icon="mdi:close" />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ px: 0 }}>

        <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>

          <Scrollbar>
            <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: '100%' }}>
              <TableHeadCustom
                order={table.order}
                orderBy={table.orderBy}
                headLabel={[
                  { id: 'action', label: t('Action') },
                  { id: 'user', label: t('User') },
                  { id: 'date', label: t('Date') },
                  { id: 'time', label: t('Time') },
                  { id: '', width: 88 },
                ]}
                rowCount={dataFiltered.length}
                onSort={table.onSort} />

              <TableBody>
                {dataFiltered
                  .slice(
                    table.page * table.rowsPerPage,
                    table.page * table.rowsPerPage + table.rowsPerPage
                  )
                  .map((row) => (
                    <TableRow
                      key={row.id}
                      hover
                      selected={table.selected.includes(row.id)}>
                      <TableCell sx={{ textTransform: 'capitalize' }}>
                        {row.action}
                      </TableCell>
                      <TableCell>
                        {`${row.user.firstName} ${row.user.lastName}`}
                      </TableCell>
                      <TableCell sx={{ textTransform: 'capitalize' }}>
                        {fDate(row.created)}
                      </TableCell>
                      <TableCell>
                        {fTime(row.created)}
                      </TableCell>
                      <TableCell>
                        <Tooltip title={t("View changes")} placement="top" arrow>
                          <IconButton onClick={() => {
                            setCurrentDiff(row.diff);
                            diffOpen.onTrue();
                          }}>
                            <Iconify icon="mynaui:git-diff" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                }

                <TableEmptyRows
                  height={denseHeight}
                  emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)} />

                <TableNoData notFound={notFound} />

              </TableBody>
            </Table>
          </Scrollbar>

        </TableContainer>

        <TablePaginationCustom
          count={dataFiltered.length}
          page={table.page}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage} />

        <Dialog
          open={diffOpen.value}
          onClose={diffOpen.onFalse}
          fullWidth
          maxWidth="sm">
          <DialogContent>
            <pre>{JSON.stringify(currentDiff, null, 2)}</pre>
          </DialogContent>
        </Dialog>

      </DialogContent>

    </Dialog>
  );
}
