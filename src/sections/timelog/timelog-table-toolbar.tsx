import { useCallback } from 'react';

import Stack from '@mui/material/Stack';
import { DatePicker } from '@mui/x-date-pickers';
import { Select, MenuItem, Checkbox, InputLabel, FormControl, OutlinedInput, SelectChangeEvent } from '@mui/material';

import { fDate } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { useGetStaff } from 'src/api/user';

import { IStaffHoursTableFilters, IStaffHoursTableFilterValue } from 'src/types/timelog';

// ----------------------------------------------------------------------

type Props = {
  filters: IStaffHoursTableFilters;
  onFilters: (name: string, value: IStaffHoursTableFilterValue) => void;
};



export default function TimelogTableToolbar({
  filters,
  onFilters,
}: Props) {
  const { t } = useTranslate();
  const { users } = useGetStaff();

  const dateRangeOptions = [
    { value: 'this_week', label: t('This week') },
    { value: 'last_week', label: t('Last week') },
    { value: 'this_month', label: t('This month') },
    { value: 'last_month', label: t('Last month') },
    { value: 'this_year', label: t('This year') },
    { value: 'last_year', label: t('Last year') },
  ];

  const handleFilterDateRange = useCallback(
    (event: SelectChangeEvent<string>) => {
      const dateRange = event.target.value;

      const startDate = new Date();
      const endDate = new Date();

      switch (dateRange) {
        case 'this_week':
          startDate.setDate(startDate.getDate() - startDate.getDay());
          endDate.setDate(startDate.getDate() + 6);
          break;
        case 'last_week':
          startDate.setDate(startDate.getDate() - startDate.getDay() - 7);
          endDate.setDate(startDate.getDate() + 6);
          break;
        case 'this_month':
          startDate.setDate(1);
          endDate.setMonth(startDate.getMonth() + 1);
          endDate.setDate(0);
          break;
        case 'last_month':
          startDate.setMonth(startDate.getMonth() - 1);
          startDate.setDate(1);
          endDate.setMonth(startDate.getMonth() + 1);
          endDate.setDate(0);
          break;
        case 'this_year':
          startDate.setMonth(0);
          startDate.setDate(1);
          endDate.setFullYear(startDate.getFullYear() + 1);
          endDate.setMonth(0);
          endDate.setDate(0);
          break;
        case 'last_year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          startDate.setMonth(0);
          startDate.setDate(1);
          endDate.setFullYear(startDate.getFullYear() + 1);
          endDate.setMonth(0);
          endDate.setDate(0);
          break;
        // If no date range is selected, clear the filters
        case '':
          onFilters('dateRange', '');
          onFilters('startDate', '');
          onFilters('endDate', '');
          return;
        default:
          break;
      }

      // Clear the filters if no date range is selected
      if (dateRange === '') {
        onFilters('startDate', '');
        onFilters('endDate', '');
        return;
      }

      // Set the selected date range
      onFilters('dateRange', dateRange as IStaffHoursTableFilterValue);

      // Set the start and end dates based on the selected date range
      onFilters('startDate', fDate(startDate, 'yyyy-MM-dd'));
      onFilters('endDate', fDate(endDate, 'yyyy-MM-dd'));
    },
    [onFilters]
  );

  const handleFilterStartDate = useCallback(
    (value: Date | null) => {
      onFilters('startDate', value ? fDate(value, 'yyyy-MM-dd') : '');
    },
    [onFilters]
  );

  const handleFilterEndDate = useCallback(
    (value: Date | null) => {
      onFilters('endDate', value ? fDate(value, 'yyyy-MM-dd') : '');
    },
    [onFilters]
  );

  const handleFilterUsers = useCallback(
    (event: SelectChangeEvent<string[]>) => {
      onFilters(
        'users',
        typeof event.target.value === 'string' ? event.target.value.split(',') : event.target.value
      );
    },
    [onFilters]
  );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}
      sx={{
        p: 2.5,
        pr: { xs: 2.5 },
      }}>

      <Stack direction="row" alignItems="center" flexGrow={1} sx={{ width: { xs: 1, md: '40%' } }}>
        <FormControl
          sx={{
            // flexShrink: 0,
            flexGrow: 1,
            width: { xs: 1, md: '1/4' },
          }}>
          <InputLabel>{t("Shortcuts")}</InputLabel>

          <Select
            value={filters.dateRange || ''}
            onChange={handleFilterDateRange}
            input={<OutlinedInput label={t("Shortcuts")} />}
            MenuProps={{
              PaperProps: {
                sx: { maxHeight: 240 },
              },
            }}>
            {dateRangeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {t(option.label)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

      </Stack>

      <Stack direction="row" alignItems="center" spacing={2} flexGrow={1} sx={{ flexShrink: 0, width: { xs: 1, md: '60%' } }}>

        <FormControl
          sx={{
            // flexShrink: 0,
            flexGrow: 1,
            width: { xs: 1, md: '1/4' },
          }}>

          <DatePicker
            value={filters.startDate ? new Date(filters.startDate) : null}
            onChange={handleFilterStartDate}
            label={t('Start date')}
            format="dd/MM/yyyy"
            slotProps={{
              actionBar: {
                actions: ['clear'],
              },
              // textField: {
              //   // fullWidth: true,

              // },
            }}
          />
        </FormControl>

        <FormControl
          sx={{
            // flexShrink: 0,
            flexGrow: 1,
            width: { xs: 1, md: '1/4' },
          }}>

          <DatePicker
            value={filters.endDate ? new Date(filters.endDate) : null}
            onChange={handleFilterEndDate}
            label={t('End date')}
            format="dd/MM/yyyy"
            slotProps={{
              actionBar: {
                actions: ['clear'],
              },
              // textField: {
              //   // fullWidth: true,

              // },
            }}
          />
        </FormControl>

        <FormControl
          sx={{
            // flexShrink: 0,
            flexGrow: 1,
            width: { xs: 1, md: '1/4' },
          }}>
          <InputLabel>{t("User")}</InputLabel>

          <Select
            multiple
            value={filters.users}
            onChange={handleFilterUsers}
            input={<OutlinedInput label={t("User")} />}
            renderValue={(selected) => selected.map((value) => t(users.find(option => option.id === value)?.firstName || '')).join(', ')}
            MenuProps={{
              PaperProps: {
                sx: { maxHeight: 240 },
              },
            }}>
            {users.map((user) => (
              <MenuItem key={user.id} value={user.id}>
                <Checkbox disableRipple size="small" checked={filters.users?.includes(user.id)} />
                {user.firstName} {user.lastName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>





      </Stack>


    </Stack>
  );
}
