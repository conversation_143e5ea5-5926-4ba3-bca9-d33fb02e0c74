'use client';

import isEqual from 'lodash/isEqual';
import { endOfMonth, startOfMonth } from 'date-fns';
import { useState, useEffect, useCallback } from 'react';

import { Box, Container } from '@mui/system';
import { Card, Table, Divider, TableRow, TableBody, TableCell, TableContainer } from '@mui/material';

import { paths } from 'src/routes/paths';

import { fDate } from 'src/utils/format-time';
import { fCurrency } from 'src/utils/format-number';

import { useTranslate } from 'src/locales';
import { useAuthContext } from 'src/auth/hooks';
import { useGetOctoratePlaceStatement } from 'src/api/octorate';

import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import { useTable, emptyRows, TableNoData, TableSkeleton, getComparator, exportToExcel, TableEmptyRows, TableHeadCustom, TablePaginationCustom } from 'src/components/table';

import { OwnerPlaceSelect } from 'src/sections/owner/owner-place-select';

import { IReservation, IStatementTableFilters, IStatementTableFilterValue } from 'src/types/statement';

import StatementTableRow from '../statement-table-row';
import StatementTableToolbar from '../statement-table-toolbar';

const defaultFilters: IStatementTableFilters = {
  startDate: fDate(startOfMonth(new Date()), 'yyyy-MM-dd'),
  endDate: fDate(endOfMonth(new Date()), 'yyyy-MM-dd'),
};

export default function StatementView() {
  const { t } = useTranslate();
  const { user: authUser } = useAuthContext();
  const [activePlace, setActivePlace] = useState<string>(authUser?.places[0]?.octorateId || '');
  const [tableData, setTableData] = useState<IReservation[]>([]);
  const [exporting, setExporting] = useState(false);

  const table = useTable({ defaultRowsPerPage: 50, defaultOrderBy: 'Arrival' })
  const [filters, setFilters] = useState(defaultFilters);

  const { statement, statementLoading, statementError } = useGetOctoratePlaceStatement(
    activePlace,
    filters.startDate,
    filters.endDate
  );

  useEffect(() => {
    if (statement) {
      setTableData(statement);
    }
  }, [statement]);

  // const dataFiltered = tableData;
  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const denseHeight = table.dense ? 56 : 56 + 20;

  const canReset = !isEqual(defaultFilters, filters);

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name: string, value: IStatementTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleExportRows = useCallback(() => {
    setExporting(true);
    const dataToExport = tableData.map((row) => ({
      [t('ID')]: row.id,
      [t('Name')]: row.guest,
      [t('Guests')]: row.totalGuests,
      [t('Arrival')]: fDate(row.start.replace('[UTC]', ''), 'yyyy-MM-dd'),
      [t('Departure')]: fDate(row.end.replace('[UTC]', ''), 'yyyy-MM-dd'),
      [t('Nights')]: row.nights,
      [t('Rent')]: fCurrency(row.totalGross) || 0,
      [t('Place')]: row.place?.name || '',
      [t('Status')]: row.status,
    }));
    exportToExcel(dataToExport, t('Statement'));
    setExporting(false);
  }, [tableData, t]);

  return (
    <Container maxWidth="lg">
      <CustomBreadcrumbs
        heading={t("Statement")}
        links={[
          { name: t('Home'), href: paths.owner.root },
          { name: t('Statement') }
        ]}
        // action={}
        sx={{
          mb: { xs: 3, md: 5 },
        }} />

      <Card sx={{ p: 3 }}>

        <Box display="flex" flexDirection="column" rowGap={3} sx={{ mb: 3 }}>

          <OwnerPlaceSelect
            activePlace={activePlace}
            setActivePlace={setActivePlace}
            places={authUser?.places || []} />

          <Divider sx={{ borderStyle: 'dashed' }} />

        </Box>

        <StatementTableToolbar
          filters={filters}
          onFilters={handleFilters}
          exporting={exporting}
          handleExportRows={handleExportRows}
        />

        <TableContainer sx={{ position: 'relative', overflow: 'unset', mt: 3 }}>
          <Scrollbar>
            <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
              <TableHeadCustom
                order={table.order}
                orderBy={table.orderBy}
                headLabel={[
                  { id: 'id', label: t('ID') },
                  { id: 'name', label: t('Name') },
                  { id: 'arrival', label: t('Arrival') },
                  { id: 'departure', label: t('Departure') },
                  { id: 'nights', label: t('Nights') },
                  { id: 'rent', label: t('Rent') },
                ]}
                rowCount={dataFiltered.length}
                onSort={table.onSort}
              />

              <TableBody>
                {!statementLoading && dataFiltered
                  .slice(
                    table.page * table.rowsPerPage,
                    table.page * table.rowsPerPage + table.rowsPerPage
                  )
                  .map((row) => (
                    <StatementTableRow
                      key={row.id}
                      row={row}
                    />
                  ))}

                {!statementLoading && !notFound && (
                  <TableRow sx={{ borderTop: (theme) => `solid 1px ${theme.palette.divider}`, borderBottom: (theme) => `solid 1px ${theme.palette.divider}` }}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Iconify icon="hugeicons:summation-square" />
                        {t('Totals')}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {dataFiltered.length} {t('Reservations')}
                      {' '}
                      ({dataFiltered.reduce((acc, curr) => acc + curr.totalGuests, 0)} {t('Guests')})
                    </TableCell>
                    <TableCell colSpan={2} />
                    <TableCell>
                      {dataFiltered.reduce((acc, curr) => acc + curr.nights, 0)}
                    </TableCell>
                    <TableCell>
                      {fCurrency(dataFiltered.reduce((acc, curr) => acc + curr.totalGross, 0))}
                    </TableCell>
                  </TableRow>
                )}

                {statementLoading && (
                  <TableSkeleton rows={10} sx={{ height: denseHeight }} />
                )}

                <TableEmptyRows
                  height={denseHeight}
                  emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)} />

                <TableNoData notFound={!statementLoading && notFound} />
              </TableBody>
            </Table>
          </Scrollbar>
        </TableContainer>

        {!statementLoading && !notFound && (
          <TablePaginationCustom
            count={dataFiltered.length}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage} />
        )}

        {statementError && <pre><Iconify icon="mdi:alert-circle-outline" color='warning.main' /> {statementError.message}</pre>}

      </Card>
    </Container>
  );
}

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: IReservation[];
  comparator: (a: any, b: any) => number;
  filters: IStatementTableFilters;
}) {
  // const { startDate, endDate } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index] as const);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  // if (startDate) {
  //   inputData = inputData.filter((user) => new Date(user.start) >= new Date(startDate));
  // }

  // if (endDate) {
  //   inputData = inputData.filter((user) => new Date(user.end) <= new Date(endDate));
  // }

  return inputData;
}
