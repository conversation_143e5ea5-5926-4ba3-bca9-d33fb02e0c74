import { Tooltip } from '@mui/material';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';

import { fDate } from 'src/utils/format-time';
import { fCurrency } from 'src/utils/format-number';

import { useTranslate } from 'src/locales';

import Label from 'src/components/label';

import { IReservation } from 'src/types/statement';

// ----------------------------------------------------------------------

type Props = {
  row: IReservation;
};

export default function StatementTableRow({
  row,
}: Props) {
  const { id, guest, start, end, nights, totalGross, totalGuests } = row;
  const { t } = useTranslate();

  return (
    <TableRow hover>

      <TableCell>
        {id}
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap' }}>
        {guest}
        {totalGuests > 1 && (
          <Tooltip
            title={
              totalGuests === 2 ?
                t('1 additional guest') :
                t('{{number}} additional guests', { number: totalGuests - 1 })
            }
            arrow
            placement="top">
            <Label variant="soft" color="info" sx={{ ml: 1 }}>
              +{totalGuests - 1}
            </Label>
          </Tooltip>
        )}
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap', textTransform: 'capitalize' }}>
        {fDate(start?.replace('[UTC]', ''))}
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap', textTransform: 'capitalize' }}>
        {fDate(end?.replace('[UTC]', ''))}
      </TableCell>

      <TableCell>
        {nights}
      </TableCell>

      <TableCell>
        {fCurrency(totalGross) || 0}
      </TableCell>

    </TableRow>
  );
}
