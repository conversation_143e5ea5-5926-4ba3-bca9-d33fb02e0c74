import { useCallback } from 'react';

import { Box } from '@mui/system';
import Stack from '@mui/material/Stack';
import { LoadingButton } from '@mui/lab';
import { FormControl } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';

import { fDate } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';

import Iconify from 'src/components/iconify';

import { IStatementTableFilters, IStatementTableFilterValue } from 'src/types/statement';

// ----------------------------------------------------------------------

type Props = {
  filters: IStatementTableFilters;
  onFilters: (name: string, value: IStatementTableFilterValue) => void;
  exporting: boolean;
  handleExportRows: () => void;
};

export default function StatementTableToolbar({
  filters,
  onFilters,
  exporting,
  handleExportRows,
}: Props) {
  const { t } = useTranslate();

  const handleFilterStartDate = useCallback(
    (value: Date | null) => {
      onFilters('startDate', value ? fDate(value, 'yyyy-MM-dd') : '');
    },
    [onFilters]
  );

  const handleFilterEndDate = useCallback(
    (value: Date | null) => {
      onFilters('endDate', value ? fDate(value, 'yyyy-MM-dd') : '');
    },
    [onFilters]
  );

  const dateError = filters.startDate && filters.endDate && new Date(filters.endDate) < new Date(filters.startDate);

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}>

      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{ width: 1 }}
      >
        <Stack direction="row" spacing={2} flexGrow={1}>
          <FormControl sx={{ width: { xs: 1, md: 240 } }}>
            <DatePicker
              label={t('Start date')}
              value={filters.startDate ? new Date(filters.startDate) : null}
              onChange={handleFilterStartDate}
              format="dd/MM/yyyy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!dateError,
                  helperText: dateError && t('Start date must be before end date'),
                },
              }}
            />
          </FormControl>

          <FormControl sx={{ width: { xs: 1, md: 240 } }}>
            <DatePicker
              label={t('End date')}
              value={filters.endDate ? new Date(filters.endDate) : null}
              onChange={handleFilterEndDate}
              format="dd/MM/yyyy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  error: !!dateError,
                  helperText: dateError && t('End date must be after start date'),
                },
              }}
            />
          </FormControl>
        </Stack>

        <LoadingButton
          loading={exporting}
          onClick={handleExportRows}
          size="large"
          variant="soft"
          startIcon={<Iconify icon="mdi:export-variant" />}
          disabled={!filters.startDate || !filters.endDate || !!dateError}>
          <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
            {t("Export")}
          </Box>
        </LoadingButton>
      </Stack>

    </Stack>
  );
}
