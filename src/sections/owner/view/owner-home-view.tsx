'use client';

import { useState } from 'react';

import { Divider } from '@mui/material';
import { Box, Container } from '@mui/system';

import { useTranslate } from 'src/locales';
import { useAuthContext } from 'src/auth/hooks';
import SeoIllustration from 'src/assets/illustrations/seo-illustration';

import AppWelcome from 'src/sections/overview/app-welcome';
import { OverviewOwnerView } from 'src/sections/overview/booking/view';

import { OwnerPlaceSelect } from '../owner-place-select';



export default function OwnerHomeView() {
  const { t } = useTranslate();

  const { user: authUser } = useAuthContext();

  const [activePlace, setActivePlace] = useState<string>(authUser?.places[0]?.octorateId || '');

  return (
    <Container maxWidth="lg">

      <Box display="flex" flexDirection="column" rowGap={3}>

        <AppWelcome
          title={`${t('Hi')} ${authUser?.firstName} 👋`}
          description={t('Welcome to your owner dashboard')}
          img={<SeoIllustration />}
        />

        <Divider sx={{ borderStyle: 'dashed' }} />
        <OwnerPlaceSelect
          activePlace={activePlace}
          setActivePlace={setActivePlace}
          places={authUser?.places || []} />
        <Divider sx={{ borderStyle: 'dashed' }} />

        <OverviewOwnerView />

      </Box>

    </Container>
  );
}
