import { useEffect, FunctionComponent } from 'react';

import { Stack } from '@mui/system';
import { Select, MenuItem, InputLabel, Typography, FormControl, OutlinedInput } from '@mui/material';

import { StorageKeys } from 'src/utils/storage-keys';

import { useTranslate } from 'src/locales';

import Iconify from 'src/components/iconify';

import { IPlace } from 'src/types/place';

interface OwnerPlaceSelectProps {
  activePlace: string;
  setActivePlace: (placeId: string) => void;
  places: IPlace[];
}

const OwnerPlaceSelect: FunctionComponent<OwnerPlaceSelectProps> = ({
  activePlace,
  setActivePlace,
  places
}) => {
  const { t } = useTranslate();
  const rememberActivePlace = (placeId: string) => {
    setActivePlace(placeId);
    // Add active place to local storage
    if ('localStorage' in window) {
      window.localStorage.setItem(StorageKeys.ActivePlace, placeId);
    }
  };

  useEffect(() => {
    // Get active place from local storage
    if ('localStorage' in window) {
      const ownerActivePlace = window.localStorage.getItem(StorageKeys.ActivePlace);
      if (ownerActivePlace) {
        setActivePlace(ownerActivePlace);
      }
    }
  }, [setActivePlace]);

  if (!places || places.length === 0) {
    return null;
  }

  if (places.length === 1) {
    return (
      <Stack direction="row" alignItems="center" columnGap={1}>
        <Iconify icon="mdi:house-city" />
        <Typography variant="subtitle2">
          {places[0]?.title} | {places[0]?.description}
        </Typography>
      </Stack>
    )
  }

  return (
    <FormControl>
      <InputLabel id="owner-place-select-label">{t("Selected property")}</InputLabel>
      <Select
        value={activePlace}
        input={<OutlinedInput label={t("Selected property")} startAdornment={<Iconify icon="mdi:house-city" style={{ marginRight: 10 }} />} />}
        onChange={(e) => rememberActivePlace(e.target.value)}>
        {places.map((p: IPlace) => (
          <MenuItem key={p.id} value={p.octorateId}>{p.title} | {p.description}</MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

export { OwnerPlaceSelect };
