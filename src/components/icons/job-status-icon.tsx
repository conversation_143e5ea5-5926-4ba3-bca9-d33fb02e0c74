import { FunctionComponent } from 'react';

import { JobStatus } from 'src/api/job';

import Iconify from '../iconify';

type JobStatusIconProps = {
  status: string;
};

const JobStatusIcon: FunctionComponent<JobStatusIconProps> = ({ status }) => {

  switch (status) {
    case JobStatus.CANCELED:
      return <Iconify icon="mdi:calendar-remove" sx={{ flexShrink: 0 }} />;
    case JobStatus.STARTED:
    case JobStatus.PAUSED:
    case JobStatus.RESUMED:
      return <Iconify icon="ic:outline-timer" sx={{ flexShrink: 0 }} />;
    case JobStatus.DONE:
    case JobStatus.INSPECTED:
      return <Iconify icon="ic:round-done-outline" sx={{ flexShrink: 0 }} />;
    case JobStatus.CREATED:
    case JobStatus.SCHEDULED:
    case JobStatus.DELAYED:
    default:
      return <Iconify icon="ic:round-today" sx={{ flexShrink: 0 }} />;
  }
}

export { JobStatusIcon };
