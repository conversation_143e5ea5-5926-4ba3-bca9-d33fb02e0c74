import { Typography } from '@mui/material';
import { Variant } from '@mui/material/styles/createTypography';

type TextVariableProps = {
  xs?: string;
  sm?: string;
  variant?: Variant;
}

const TextVariable = ({ xs, sm, variant }: TextVariableProps) => {
  const breakpoints = [
    { key: 'xs', value: xs, display: { xs: 'block', sm: 'none' } },
    { key: 'sm', value: sm, display: { xs: 'none', sm: 'block' } },
  ];

  return (
    <>
      {breakpoints.map(({ key, value, display }) =>
        value && (
          <Typography
            key={key}
            variant={variant}
            sx={{ display }}
          >
            {value}
          </Typography>
        )
      )}
    </>
  );
};

export { TextVariable };
